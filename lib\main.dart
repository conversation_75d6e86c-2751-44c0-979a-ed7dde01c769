import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myapp/components/onboarding_screen.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/signup_page.dart';
import 'package:myapp/admin/admin_login.dart';
import 'package:myapp/therapist_screen.dart';
import 'package:myapp/home_page.dart';
import 'package:myapp/mood_journal.dart';
import 'package:myapp/journaling.dart';
import 'package:myapp/ai_analysis_screen.dart';
import 'package:myapp/payment_screen.dart';
import 'package:myapp/appointment.dart';
import 'package:myapp/video_call_screen.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/services/stripe_service.dart';
import 'package:myapp/services/localization_service.dart';
import 'package:myapp/core/error_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize global error handling first
  try {
    GlobalErrorHandler.initialize();
    debugPrint('✅ Global error handler initialized');
  } catch (e) {
    debugPrint('⚠️ Error handler initialization failed: $e');
  }

  // Initialize Stripe with error handling to prevent app crashes
  try {
    await StripeService.init();
    debugPrint('✅ Stripe initialized successfully');
  } catch (e) {
    debugPrint('⚠️ Stripe initialization failed: $e');
    // App continues to work even if Stripe fails to initialize
  }

  // Initialize localization service
  try {
    await LocalizationService().loadSavedLanguage();
    debugPrint('✅ Localization service initialized');
  } catch (e) {
    debugPrint('⚠️ Localization initialization failed: $e');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MindEase',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      initialRoute: '/',
      routes: {
        '/': (context) => const OnboardingScreen(),
        '/login': (context) => const LoginPage(),
        '/signup': (context) => const SignupPage(),
        '/admin': (context) => const AdminLoginScreen(),
        '/therapist': (context) => const CreateTherapistPage(),
      },
      onGenerateRoute: (settings) {
        try {
          final args = settings.arguments as Map<String, dynamic>?;

          switch (settings.name) {
            case '/home':
              return MaterialPageRoute(
                builder: (context) =>
                    HomeScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/journaling':
              return MaterialPageRoute(
                builder: (context) =>
                    JournalScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/mood_journal':
              return MaterialPageRoute(
                builder: (context) => const MoodJournalScreen(),
              );
            case '/analysis':
              return MaterialPageRoute(
                builder: (context) =>
                    AIAnalysisScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/payment':
              return MaterialPageRoute(
                builder: (context) => PaymentScreen(
                  userId: args?['userId'] ?? '',
                  amount: args?['amount'] ?? 0.0,
                  description: args?['description'] ?? 'Mental Health Service',
                  therapistId: args?['therapistId'],
                  appointmentId: args?['appointmentId'],
                ),
              );
            case '/appointment':
              return MaterialPageRoute(
                builder: (context) =>
                    MyAppointmentsScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/video_call':
              return MaterialPageRoute(
                builder: (context) => VideoCallScreen(
                  meetingLink: args?['meetingLink'] ?? '',
                  therapistName: args?['therapistName'] ?? 'Therapist',
                  userId: args?['userId'] ?? 'guest',
                ),
              );
            default:
              return MaterialPageRoute(
                builder: (context) => const OnboardingScreen(),
              );
          }
        } catch (e) {
          debugPrint('Route generation error: $e');
          return MaterialPageRoute(
            builder: (context) => const OnboardingScreen(),
          );
        }
      },
      onUnknownRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => const OnboardingScreen(),
        );
      },
    );
  }
}

class TestScreen extends StatelessWidget {
  const TestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('MindEase - Working!'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 100,
              color: Colors.green,
            ),
            SizedBox(height: 20),
            Text(
              'MindEase App Started Successfully!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'The app is now working without crashes.',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 30),
            Text(
              'Backend Server Status:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Make sure your server is running on port 3000',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
