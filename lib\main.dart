import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myapp/components/onboarding_screen.dart';
import 'package:myapp/login_page.dart';
import 'package:myapp/signup_page.dart';
import 'package:myapp/admin/admin_login.dart';
import 'package:myapp/therapist_screen.dart';
import 'package:myapp/home_page.dart';
import 'package:myapp/mood_journal.dart';
import 'package:myapp/journaling.dart';
import 'package:myapp/ai_analysis_screen.dart';
import 'package:myapp/payment_screen.dart';
import 'package:myapp/appointment.dart';
import 'package:myapp/video_call_screen.dart';
import 'package:myapp/theme/app_theme.dart';
import 'package:myapp/services/stripe_service.dart';
import 'package:myapp/services/localization_service.dart';
import 'package:myapp/core/error_handler.dart';

void main() async {
  // Wrap everything in a try-catch to prevent startup crashes
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize services with individual error handling
    await _initializeServices();

    runApp(const MyApp());
  } catch (e) {
    debugPrint('🚨 CRITICAL STARTUP ERROR: $e');
    // Run minimal app even if initialization fails
    runApp(const MinimalApp());
  }
}

Future<void> _initializeServices() async {
  // Initialize global error handling first
  try {
    GlobalErrorHandler.initialize();
    debugPrint('✅ Global error handler initialized');
  } catch (e) {
    debugPrint('⚠️ Error handler initialization failed: $e');
  }

  // Initialize Stripe with error handling to prevent app crashes
  try {
    await StripeService.init();
    debugPrint('✅ Stripe initialized successfully');
  } catch (e) {
    debugPrint('⚠️ Stripe initialization failed: $e');
    // App continues to work even if Stripe fails to initialize
  }

  // Initialize localization service
  try {
    await LocalizationService().loadSavedLanguage();
    debugPrint('✅ Localization service initialized');
  } catch (e) {
    debugPrint('⚠️ Localization initialization failed: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MindEase',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      initialRoute: '/',
      routes: {
        '/': (context) => const OnboardingScreen(),
        '/login': (context) => const LoginPage(),
        '/signup': (context) => const SignupPage(),
        '/admin': (context) => const AdminLoginScreen(),
        '/therapist': (context) => const CreateTherapistPage(),
      },
      onGenerateRoute: (settings) {
        try {
          final args = settings.arguments as Map<String, dynamic>?;

          switch (settings.name) {
            case '/home':
              return MaterialPageRoute(
                builder: (context) =>
                    HomeScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/journaling':
              return MaterialPageRoute(
                builder: (context) =>
                    JournalScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/mood_journal':
              return MaterialPageRoute(
                builder: (context) => const MoodJournalScreen(),
              );
            case '/analysis':
              return MaterialPageRoute(
                builder: (context) =>
                    AIAnalysisScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/payment':
              return MaterialPageRoute(
                builder: (context) => PaymentScreen(
                  userId: args?['userId'] ?? '',
                  amount: args?['amount'] ?? 0.0,
                  description: args?['description'] ?? 'Mental Health Service',
                  therapistId: args?['therapistId'],
                  appointmentId: args?['appointmentId'],
                ),
              );
            case '/appointment':
              return MaterialPageRoute(
                builder: (context) =>
                    MyAppointmentsScreen(userId: args?['userId'] ?? 'guest'),
              );
            case '/video_call':
              return MaterialPageRoute(
                builder: (context) => VideoCallScreen(
                  meetingLink: args?['meetingLink'] ?? '',
                  therapistName: args?['therapistName'] ?? 'Therapist',
                  userId: args?['userId'] ?? 'guest',
                ),
              );
            default:
              return MaterialPageRoute(
                builder: (context) => const OnboardingScreen(),
              );
          }
        } catch (e) {
          debugPrint('Route generation error: $e');
          return MaterialPageRoute(
            builder: (context) => const OnboardingScreen(),
          );
        }
      },
      onUnknownRoute: (settings) {
        return MaterialPageRoute(
          builder: (context) => const OnboardingScreen(),
        );
      },
    );
  }
}

// Minimal app that always works - fallback for critical errors
class MinimalApp extends StatelessWidget {
  const MinimalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MindEase',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('MindEase'),
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.psychology,
                size: 80,
                color: Colors.blue,
              ),
              SizedBox(height: 20),
              Text(
                'MindEase',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
              SizedBox(height: 10),
              Text(
                'Mental Health Support App',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 30),
              Text(
                'App is starting...',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: 10),
              CircularProgressIndicator(),
            ],
          ),
        ),
      ),
    );
  }
}
